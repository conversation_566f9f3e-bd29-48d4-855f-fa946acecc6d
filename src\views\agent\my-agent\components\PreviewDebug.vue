<script setup lang="ts">
import { ref } from 'vue'
import deleteIcon from '@/assets/agent/clear.svg'
import documentIcon from '@/assets/agent/add.svg'
import microphoneIcon from '@/assets/agent/microphone.svg'
import { useSettingStore, useUserStore } from '@/store'
import TextComponent from '@/views/chat/components/Message/Text.vue'
import AvatarComponent from '@/views/chat/components/Message/Avatar.vue'

// 定义提示词测试接口入参类型
interface PromptTestRequest {
  /** 系统消息 */
  systemMessage: string
  /** 用户消息 */
  userMessage: string
  /** 模型配置 */
  modelConfig: {
    model: string
    temperature: number
    maxTokens: number
    topP: number
    frequencyPenalty: number
    presencePenalty: number
    systemMessage: string
    switch: boolean
    reasoningEffort: string
    modelId?: number
  }
  /** 模型ID */
  modelId: number
  /** 聊天消息列表 */
  chatMessageList: Array<{ role: string; content: string }>
  /** 知识库ID列表（可选） */
  kbIds?: string[]
}

const props = defineProps<{
  modelConfig: {
    model: string
    temperature: number
    maxTokens: number
    topP: number
    frequencyPenalty: number
    presencePenalty: number
    systemMessage: string
    switch: boolean
    reasoningEffort: string
    modelId?: number // 添加可选的modelId属性
  }
  promptContent: string
}>()

const settingStore = useSettingStore()

// 测试消息相关
const testMessage = ref('')
// 此处不需要初始化数据
const messages = ref<Array<{ role: string; content: string }>>([])

const loading = ref(false)
let controller = new AbortController()

// 滚动到底部
const scrollToBottom = () => {
  const chatMessages = document.querySelector('.chat-messages')
  if (chatMessages) {
    chatMessages.scrollTop = chatMessages.scrollHeight
  }
}

// 处理测试消息发送
// todo 用src\views\chat\hooks\useConversationActions.ts的streamResponse处理
const handleTestMessage = async () => {
  if (!testMessage.value.trim()) {
    return
  }
  if (loading.value) {
    return
  }

  // 添加用户消息
  messages.value.push({
    role: 'user',
    content: testMessage.value,
  })

  // 准备发送消息
  const userMessage = testMessage.value
  testMessage.value = ''
  loading.value = true
  controller = new AbortController()

  let modelId = props.modelConfig.modelId
  if (!props.modelConfig.modelId) {
    const modelData = await settingStore.fetchDefaultModel(true)
    modelId = modelData.chatModelId
  }
  try {
    const requestData: PromptTestRequest = {
      systemMessage: props.promptContent,
      userMessage,
      modelConfig: props.modelConfig,
      modelId: modelId!,
      chatMessageList: messages.value,
    }
    const token = useUserStore().token
    // resolved 这个接口入参建一个ts类型
    const response = await fetch(
      `${import.meta.env.VITE_APP_BASE_API}/biz/conversation/promptTest`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
          Clientid: import.meta.env.VITE_APP_CLIENT_ID,
        },
        body: JSON.stringify(requestData),
        signal: controller.signal,
      },
    )

    if (!response.ok) {
      throw new Error('网络请求失败')
    }

    const reader = response.body?.getReader()
    const decoder = new TextDecoder()
    let accumulatedContent = ''

    if (!reader) {
      throw new Error('无法获取响应流')
    }

    while (true) {
      const { done, value } = await reader.read()
      if (done) {
        break
      }

      const chunk = decoder.decode(value)
      const lines = chunk.split('\n')

      for (const line of lines) {
        if (line.startsWith('data:')) {
          const dataContent = line.substring(5).trim()
          if (!dataContent) {
            continue
          }

          try {
            const data = JSON.parse(dataContent)
            if (data.content !== undefined) {
              accumulatedContent += data.content

              // 更新或添加AI回复消息
              const lastMessage = messages.value[messages.value.length - 1]
              if (lastMessage.role === 'assistant') {
                lastMessage.content = accumulatedContent
              } else {
                messages.value.push({
                  role: 'assistant',
                  content: accumulatedContent,
                })
              }

              // 如果是结束标记
              if (data.isEnd) {
                loading.value = false
              }

              // 滚动到底部
              scrollToBottom()
            }
          } catch (error) {
            console.error('解析响应数据失败:', error)
          }
        }
      }
    }
  } catch (error: any) {
    if (error.message === 'canceled') {
      messages.value.push({
        role: 'assistant',
        content: '消息已取消发送',
      })
    } else {
      messages.value.push({
        role: 'assistant',
        content: `发生错误：${error.message}`,
      })
    }
  } finally {
    loading.value = false
    scrollToBottom()
  }
}

// 清空对话
const handleClear = () => {
  messages.value = []
}

// 处理按键事件
const handleKeyPress = (e: Event) => {
  const event = e as KeyboardEvent
  if (event.key === 'Enter' && !event.shiftKey) {
    e.preventDefault()
    handleTestMessage()
  }
}
</script>

<template>
  <div class="preview-container">
    <div class="chat-bg">
      <div class="flex items-center justify-center">
        <img src="@/assets/agent/chat-bg.png" alt="chat-bg" />
        <div class="t">测试BOT</div>
      </div>
    </div>
    <div class="chat-messages">
      <div v-for="(message, index) in messages" :key="index" class="message" :class="message.role">
        <div class="avatar">
          <!-- resolved 此处使用对应的用户头像和系统头像 -->
          <AvatarComponent :image="message.role !== 'assistant'" />
        </div>
        <div class="message-content">
          <TextComponent
            v-if="message.role === 'assistant'"
            :inversion="false"
            :error="false"
            :text="message.content"
            :loading="index === messages.length - 1 && loading"
          />
          <div v-else class="message-text">{{ message.content }}</div>
        </div>
      </div>
    </div>
    <div class="chat-input">
      <el-tooltip content="清空对话" placement="top">
        <div class="del-btn" @click="handleClear">
          <img :src="deleteIcon" alt="delete" />
        </div>
      </el-tooltip>
      <div class="input-box">
        <el-input
          v-model="testMessage"
          type="textarea"
          :rows="1"
          :autosize="{ minRows: 1, maxRows: 5 }"
          :placeholder="loading ? '正在生成回复...' : '请输入对话内容'"
          resize="none"
          @keydown.enter.prevent="handleKeyPress"
        >
          <!-- :disabled="loading" -->
        </el-input>
        <!-- <el-tooltip content="文件" placement="top">
          <div class="file-btn">
            <img :src="documentIcon" alt="document" />
          </div>
        </el-tooltip>
        <el-tooltip content="录音" placement="top">
          <div class="record-btn ml-2">
            <img :src="microphoneIcon" alt="microphone" />
          </div>
        </el-tooltip> -->
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  .chat-bg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
    pointer-events: none;
    z-index: 0;

    div {
      img {
        width: 64px;
        height: 64px;
        object-fit: contain;
      }

      .t {
        font-size: 20px;
        color: #4d5bec;
        line-height: 28px;
        text-align: left;
        font-style: bold;
        opacity: 0.3;
      }
    }
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 5px;
    }

    &::-webkit-scrollbar-thumb {
      background: #e0e0e0;
      border-radius: 2px;
      cursor: pointer;

      &:hover {
        background: #c0c0c0;
      }
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    // 当不滚动时隐藏滚动条
    &:hover {
      &::-webkit-scrollbar-thumb {
        background: #e0e0e0;
      }
    }

    .message {
      position: relative;
      z-index: 1;
      display: flex;
      margin-bottom: 16px;
      // padding: 0 16px;

      &.user {
        .message-content {
          margin-left: 12px;
          max-width: 80%;

          .message-text {
            display: inline-block;
            background: #4865e8;
            color: #fff;
            border-radius: 4px;
            padding: 12px;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-break: break-word;
          }
        }
      }

      &.assistant {
        .message-content {
          margin-left: 12px;
          max-width: 80%;
          .message-reply {
            display: inline-block;
            background: #f0f2f7; // 换一个美观的灰色底色
          }
        }
      }

      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        overflow: hidden;
        margin-right: 12px;

        :deep(.n-avatar) {
          background-color: transparent;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .message-content {
        flex: 1;
      }
    }
  }

  .chat-input {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    // padding: 16px;
    .del-btn {
      width: 44px;
      height: 44px;
      background: #ffffff;
      border-radius: 24px;
      border: 1px solid #dadde8;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      img {
        width: 24px;
        height: 24px;
      }
    }
    .input-box {
      flex: 1;
      margin-left: 12px;
      padding: 5px 8px;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      width: 332px;
      min-height: 44px;
      background: #ffffff;
      border-radius: 24px;
      border: 1px solid #dadde8;
      :deep(.el-textarea) {
        border: none !important;
        box-shadow: none !important;
        .el-textarea__inner {
          border: none !important;
          box-shadow: none !important;
        }
      }
      .file-btn,
      .record-btn {
        width: 32px;
        height: 32px;
        background: #ffffff;
        border-radius: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        img {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
</style>
